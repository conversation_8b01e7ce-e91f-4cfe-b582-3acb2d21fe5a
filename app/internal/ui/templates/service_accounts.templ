package templates

import (
    "github.com/information-sharing-networks/signalsd/app/internal/ui/client"
)

templ ManageServiceAccounts() {
	@BaseLayout("Manage Service Accounts") {
		@Navigation()
		<div class="page-container">
			<h1 class="page-title">Manage Service Accounts</h1>

			<!-- Create New Service Account Section -->
			<div class="card mb-6">
				<div class="card-header">
					<h3 class="card-title">Create New Service Account</h3>
				</div>
				<div class="card-body">
					<form hx-post="/ui-api/create-service-account" hx-target="#create-result" class="space-y-4">
						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div class="form-group">
								<label for="email" class="form-label">Contact Email</label>
								<input
									id="email"
									name="email"
									type="email"
									class="form-input"
									placeholder="Contact email for the service account"
								/>
							</div>
							<div class="form-group">
								<label for="organization" class="form-label">Client Organization</label>
								<input
									id="organization"
									name="organization"
									type="text"
									required
									class="form-input"
									placeholder="Client organization name"
								/>
							</div>
						</div>
						<div class="form-group">
							<button type="submit" class="btn btn-primary">
								Create Service Account
							</button>
						</div>
					</form>
				</div>
			</div>

			<div id="create-result">
				<!-- Results will appear here -->
			</div>

			<!-- Reissue Service Account Credentials Section -->
			<div class="card mb-6">
				<div class="card-header">
					<h3 class="card-title">Reissue Service Account Credentials</h3>
				</div>
				<div class="card-body">
					@WarningAlert("Warning: Reissuing credentials will revoke any existing client secrets for the service account, including any unused one-time setup URLs.")
					<p class="text-muted mb-4">Select an existing service account to reissue its credentials.</p>

					<div class="form-group">
						<label for="service-account-dropdown" class="form-label">Service Account</label>
						@ServiceAccountPlaceholder()
					</div>

					<!-- Load service account options on page load -->
					<div hx-get="/ui-api/service-account-options"
						hx-trigger="load"
						hx-target="#service-account-dropdown"
						hx-swap="outerHTML"
						style="display: none;">
					</div>

					<div id="reissue-btn-container" class="form-group mt-4">
						@ReissueButton(false)
					</div>

					<div id="reissue-result">
						<!-- Results will appear here -->
					</div>
				</div>
			</div>

		</div>
	}
}

templ ServiceAccountCreationSuccess(response client.CreateServiceAccountResponse) {
	<div class="card">
		<div class="card-body">
			@SuccessAlert("Service Account created successfully!")
			<div class="mt-4 space-y-2">
				<p><strong>Client ID:</strong> <code class="text-sm bg-gray-100 px-2 py-1 rounded">{ response.ClientID }</code></p>
				<p><strong>Account ID:</strong> <code class="text-sm bg-gray-100 px-2 py-1 rounded">{ response.AccountID.String() }</code></p>
				<p><strong>Setup URL:</strong>
					<code class="text-xs bg-gray-100 px-2 py-1 rounded block break-all">{ response.SetupURL }</code>
				</p>
			</div>
		</div>
	</div>
}

templ ServiceAccountReissueSuccess(response client.ReissueServiceAccountResponse) {
	<div class="card">
		<div class="card-body">
			@SuccessAlert("Service Account credentials reissued")
			<div class="mt-4 space-y-2">
				<p><strong>Client ID:</strong> <code class="text-sm bg-gray-100 px-2 py-1 rounded">{ response.ClientID }</code></p>
				<p><strong>New Setup URL:</strong>
					<code class="text-sm bg-gray-100 px-2 py-1 rounded block break-all">{ response.SetupURL }</code>
				</p>
				<p class="text-sm text-gray-600 mt-2">
					<strong>Note:</strong> All previous client secrets have been revoked. The owner of this account can use the link above to retrieve their new client secret (the link can only be used once and expires in 48 hours).
				</p>
			</div>
		</div>
	</div>
	<!-- Update button to disabled state using out-of-band swap -->
	<div hx-swap-oob="true" id="reissue-btn-container">
		@ReissueButton(false, "Credentials Reissued")
	</div>
}

templ ReissueButton(isEnabled bool, customText ...string) {
	<button
		id="reissue-btn"
		hx-post="/ui-api/reissue-service-account"
		hx-target="#reissue-result"
		hx-include="#service-account-dropdown"
		class="btn"
		if !isEnabled {
			class="btn opacity-50 cursor-not-allowed"
		}
		disabled?={ !isEnabled }
	>
		if len(customText) > 0 {
			{ customText[0] }
		} else {
			Reissue Credentials
		}
	</button>
}

