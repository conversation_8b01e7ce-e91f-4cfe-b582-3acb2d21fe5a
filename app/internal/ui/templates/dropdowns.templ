package templates

import (
    "strings"
    "github.com/information-sharing-networks/signalsd/app/internal/ui/types"
)




templ AccountTypeSelector() {
    <select
        id="account-type"
        name="account-type"
        required
        class="form-select"
        hx-get="/ui-api/account-identifier-field"
        hx-target="#account-identifier-container"
        hx-swap="outerHTML"
        hx-trigger="change"
        hx-include="this"
    >
        <option value="">Select Account Type...</option>
        <option value="user">User</option>
        <option value="service-account">Service Account</option>
    </select>
}

// Placeholder input field (disabled)
templ AccountIdentifierPlaceholder() {
    <div id="account-identifier-container">
        <input
            id="account-identifier"
            name="account-identifier"
            type="text"
            placeholder="Select account type first..."
            disabled
            class="form-input"
        />
    </div>
}

// User dropdown (replaces placeholder when user is selected)
templ AccountIdentifierSelector(users []types.UserOption) {
    <div id="account-identifier-container">
        <select
            id="account-identifier"
            name="account-identifier"
            required
            class="form-select"
        >
            <option value="">Select User...</option>
            for _, user := range users {
                <option value={ user.Email }>{ user.Email } ({ user.UserRole })</option>
            }
        </select>
    </div>
}

// Service account dropdown for ISN account management
templ AccountIdentifierServiceAccountSelector(serviceAccounts []types.ServiceAccountOption) {
    <div id="account-identifier-container">
        @ServiceAccountSelector(serviceAccounts, "account-identifier", "account-identifier")
    </div>
}


// service account selector component with optional attributes
templ ServiceAccountSelector(serviceAccounts []types.ServiceAccountOption, id, name string, attrs ...templ.Attributes) {
	<select
		id={ id }
		name={ name }
		required
		class="form-select"
		if len(attrs) > 0 {
			{ attrs[0]... }
		}
	>
		<option value="">Select Service Account...</option>
		for _, account := range serviceAccounts {
			<option value={ account.ClientID }>{ account.ClientOrganization } / { account.ClientContactEmail } ({ account.ClientID })</option>
		}
	</select>
}

// Service account placeholder (disabled, gets replaced when loaded)
templ ServiceAccountPlaceholder() {
	<select
		id="service-account-dropdown"
		name="service-account-dropdown"
		disabled
		class="form-select"
	>
		<option value="">Loading service accounts...</option>
	</select>
}

// Service account dropdown with options (replaces placeholder)
templ ServiceAccountSelectorDonotuse(serviceAccounts []types.ServiceAccountOption) {
	<select
		id="service-account-dropdown"
		name="service-account-dropdown"
		required
		class="form-select"
		hx-get="/ui-api/reissue-btn-state"
		hx-trigger="change"
		hx-target="#reissue-btn-container"
		hx-swap="innerHTML"
		hx-include="this"
	>
		<option value="">Select Service Account...</option>
		for _, account := range serviceAccounts {
			<option value={ account.ClientID }>{ account.ClientOrganization } / { account.ClientContactEmail } ({ account.ClientID })</option>
		}
	</select>
	<!-- Clear alerts when dropdown selection changes -->
	<div hx-get="/ui-api/clear-alerts"
		hx-trigger="change from:#service-account-dropdown"
		hx-target="#reissue-result"
		hx-swap="innerHTML"
		style="display: none;">
	</div>
}


// User dropdown for password reset functionality
templ UserOptionsGeneratePasswordLink(users []types.UserOption) {
	<select
		id="user-dropdown"
		name="user-dropdown"
		required
		class="form-select"
		hx-get="/ui-api/generate-password-reset-btn-state"
		hx-trigger="change"
		hx-target="#generate-password-reset-btn-container"
		hx-swap="innerHTML"
		hx-include="this"
	>
		<option value="">Select User...</option>
		for _, user := range users {
			<option value={ user.Email + "|" + user.UserRole }>{ user.Email } ({ user.UserRole })</option>
		}
	</select>
	<!-- Clear alerts when dropdown selection changes -->
	<div hx-get="/ui-api/clear-alerts"
		hx-trigger="change from:#user-dropdown"
		hx-target="#generate-password-reset-result"
		hx-swap="innerHTML"
		style="display: none;">
	</div>
}

templ IsnAccountPermissionsDropdown() {
    <select
        id="permission"
        name="permission"
        required
        class="form-select"
    >
        <option value="">Select Permission...</option>
        <option value="none">None - Revoke access to the ISN</option>
        <option value="read">Read - Can view signals</option>
        <option value="write">Write - Can create and view signals</option>
    </select>
}

// ISN selector dropdown
templ IsnSelector(isns []types.IsnOption) {
    <select
        id="isn-slug"
        name="isn-slug"
        required
        class="form-select"
    >
        <option value="">Select ISN...</option>
        for _, isn := range isns {
            <option value={ isn.Slug }>{ strings.ReplaceAll(isn.Slug, "-", " ") }</option>
        }
    </select>
}



// the list of available signal types for the selected ISN
templ SignalTypeOptions(signalTypes []types.SignalTypeOption) {
	<select
		id="signal-type-slug"
		name="signal-type-slug"
		required
		hx-post="/ui-api/signal-type-version-options"
		hx-target="#sem-ver"
		hx-swap="outerHTML"
		hx-trigger="change"
		hx-include="#isn-slug, this"
		class="form-select"
	>
		<option value="">Select Signal Type...</option>
		for _, signalType := range signalTypes {
			<option value={ signalType.Slug }>{ strings.ReplaceAll(signalType.Slug, "-", " ") }</option>
		}
	</select>
}

//the list of available versions for the selected signal type
templ SignalTypeVersionOptions(versions []types.VersionOption) {
	<select
		id="sem-ver"
		name="sem-ver"
		required
		class="form-select"
	>
		<option value="">Select Version...</option>
		for _, version := range versions {
			<option value={ version.Version }>{ version.Version }</option>
		}
	</select>
}