package templates

import (
	"github.com/information-sharing-networks/signalsd/app/internal/ui/types"
)

templ IsnAccountManagementPage(isns []types.IsnOption) {
	@BaseLayout("ISN Account Management") {
		@Navigation()
		<div class="page-container">
			<h1 class="page-title">ISN Account Management</h1>
			<p class="text-muted mb-6">Grant or revoke access to Information Sharing Networks.</p>

			<div class="card mb-6">
				<div class="card-header">
					<h3 class="card-title">Change ISN permissions for an account</h3>
				</div>
				<div class="card-body">
					<form hx-post="/ui-api/update-isn-account" hx-target="#update-access-result" class="space-y-4">
						<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
							<div class="form-group">
								<label for="isn-slug" class="form-label">ISN</label>
								@IsnSelector(isns)
							</div>
							<div class="form-group">
								<label for="account-type" class="form-label">Account Type</label>
								@AccountTypeSelector()
							</div>
							
							<div class="form-group">
								<label for="account-identifier" class="form-label">Account</label>
								@AccountIdentifierPlaceholder()
							</div>
							<div class="form-group">
								<label for="permission" class="form-label">Permission Level</label>
								@IsnAccountPermissionsDropdown()
							</div>
						</div>
						<div class="form-group">
							<button type="submit" class="btn btn-primary">Submit</button>
						</div>
					</form>
				</div>
			</div>

			<div id="update-access-result">
				<!-- Results will appear here -->
			</div>
		</div>
	}	
}